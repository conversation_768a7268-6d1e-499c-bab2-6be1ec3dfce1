import React, { useState, useEffect, Suspense, lazy } from 'react';
import PixelLoader from './components/PixelLoader';
import Navbar from './components/Navbar';
import HeroSection from './components/HeroSection';
import FeaturesGuideSection from './components/FeaturesGuideSection';
import Footer from './components/Footer';
import BrowserCompatibilityCheck from './components/BrowserCompatibilityCheck';
import ResourcePreloader from './components/ResourcePreloader';
import ErrorBoundary from './components/ErrorBoundary';
import authManager from './services/authManager';
import './utils/emojiCompatibility'; // 引入emoji兼容性处理
import './utils/domErrorHandler'; // 引入DOM错误处理
import './utils/preloadTest'; // 引入预加载测试工具
import './utils/errorCheck'; // 引入错误检查工具

import { getLdapApiUrl } from './config/apiConfig';

// 懒加载非首屏组件 - 按需加载策略
const APIKeySection = lazy(() => import(/* webpackChunkName: "api-key" */ './components/APIKeySection'));
const WebIDESection = lazy(() => import(/* webpackChunkName: "webide" */ './components/WebIDESection'));
const DownloadsSection = lazy(() => import(/* webpackChunkName: "downloads" */ './components/DownloadsSection'));
const DocumentationSection = lazy(() => import(/* webpackChunkName: "documentation" */ './components/DocumentationSection'));
const NewsSection = lazy(() => import(/* webpackChunkName: "news" */ './components/NewsSection'));
const LoginModal = lazy(() => import(/* webpackChunkName: "login" */ './components/LoginModal'));

// 加载占位组件
const LoadingPlaceholder = ({ height = "500px" }) => (
  <div className={`flex items-center justify-center bg-gray-900/50 rounded-lg animate-pulse`} style={{ height }}>
    <div className="text-cyan-400 text-lg">Loading...</div>
  </div>
);

function App() {
  const [isLoading, setIsLoading] = useState(true);
  const [user, setUser] = useState(null);
  const [showLoginModal, setShowLoginModal] = useState(false);
  const [authMessage, setAuthMessage] = useState('');

  useEffect(() => {
    // 使用 authManager 检查用户登录状态
    const currentUser = authManager.getCurrentUser();
    if (currentUser) {
      setUser(currentUser);
    }

    // 监听认证状态变化事件
    const handleAuthStateChange = (event) => {
      const { type, reason, remaining } = event.detail;
      
      switch (type) {
        case 'expired':
          setUser(null);
          setAuthMessage('登录已过期，请重新登录');
          setTimeout(() => setAuthMessage(''), 5000);
          break;
          
        case 'logout':
          setUser(null);
          if (reason === 'timeout') {
            setAuthMessage('登录超时，已自动注销');
            setTimeout(() => setAuthMessage(''), 5000);
          }
          break;
          
        case 'expiring':
          setAuthMessage(`登录将在${remaining.formattedTime}后过期`);
          setTimeout(() => setAuthMessage(''), 10000);
          break;
          
        default:
          break;
      }
    };

    // 处理显示登录模态框事件
    const handleShowLoginModal = () => {
      setShowLoginModal(true);
    };

    window.addEventListener('authStateChange', handleAuthStateChange);
    window.addEventListener('showLoginModal', handleShowLoginModal);
    
    return () => {
      window.removeEventListener('authStateChange', handleAuthStateChange);
      window.removeEventListener('showLoginModal', handleShowLoginModal);
    };
  }, []);

  const handleLoaderComplete = () => {
    setIsLoading(false);
  };

  const handleLogin = (userData) => {
    // 使用 authManager 保存登录状态
    authManager.saveUserLogin(userData);
    setUser(userData);
    setShowLoginModal(false);
    
    // 显示登录成功消息
    const status = authManager.getLoginStatus();
    if (status.remaining && !status.remaining.expired) {
      setAuthMessage(`登录成功！登录有效期: ${status.remaining.formattedTime}`);
      setTimeout(() => setAuthMessage(''), 5000);
    }
  };

  const handleLogout = () => {
    // 使用 authManager 注销
    authManager.logout();
    setUser(null);
  };

  return (
    <ErrorBoundary>
      {/* 资源预加载 */}
      <ResourcePreloader />
      
      {/* 浏览器兼容性检测 */}
      <BrowserCompatibilityCheck />
      
      {/* 像素加载动画 */}
      <PixelLoader onComplete={handleLoaderComplete} />

      {/* 主内容 */}
      {!isLoading && (
            <div className="min-h-screen bg-black text-white">
              {/* 性能模式切换提示 */}
              {authMessage && (
                <div className="fixed top-4 right-4 z-40 bg-cyan-900/90 text-cyan-100 px-4 py-2 rounded-lg shadow-lg">
                  {authMessage}
                </div>
              )}
              
              {/* 导航栏 */}
              <Navbar 
                user={user} 
                onLogin={() => setShowLoginModal(true)} 
                onLogout={handleLogout}
              />

              {/* 主要内容区域 */}
              <main>
                {/* 英雄区域 */}
                <div id="home">
                  <HeroSection />
                </div>

                {/* 平台数据 */}
                <div id="features">
                  <FeaturesGuideSection />
                </div>


                {/* API密钥管理 */}
                <Suspense fallback={<LoadingPlaceholder />}>
                  <APIKeySection 
                    user={user} 
                    onLogin={() => setShowLoginModal(true)}
                  />
                </Suspense>

                {/* Web IDE */}
                <div id="webide">
                  <section className="py-20 px-4 sm:px-6 lg:px-8">
                    <div className="max-w-7xl mx-auto">
                      <div className="text-center mb-16">
                        <h2 className="text-4xl font-black text-transparent bg-clip-text bg-gradient-to-r from-cyan-400 via-blue-500 to-purple-600 mb-6">
                          Web IDE
                        </h2>
                        <p className="text-xl text-gray-400 max-w-2xl mx-auto">
                          在线开发环境，基于 VSCode 为WIN7老电脑开发者提供完整的AI代码编辑和开发体验
                          若需获得完整体验，请安装VSCode及AI插件
                        </p>
                      </div>
                      <Suspense fallback={<LoadingPlaceholder height="600px" />}>
                        <WebIDESection 
                          user={user} 
                          onLogin={() => setShowLoginModal(true)}
                        />
                      </Suspense>
                    </div>
                  </section>
                </div>

                {/* 工具下载 */}
                <div id="downloads">
                  <Suspense fallback={<LoadingPlaceholder />}>
                    <DownloadsSection 
                      user={user} 
                      onLogin={() => setShowLoginModal(true)}
                    />
                  </Suspense>
                </div>

                {/* 文档中心 */}
                <div id="docs">
                  <Suspense fallback={<LoadingPlaceholder />}>
                    <DocumentationSection />
                  </Suspense>
                </div>

                {/* 最新动态 */}
                <div id="news">
                  <Suspense fallback={<LoadingPlaceholder />}>
                    <NewsSection />
                  </Suspense>
                </div>
              </main>

              {/* 页脚 */}
              <Footer />
              
              {/* 登录模态框 */}
              {showLoginModal && (
                <Suspense fallback={<div>Loading...</div>}>
                  <LoginModal 
                    isOpen={showLoginModal}
                    onLogin={handleLogin}
                    onClose={() => setShowLoginModal(false)}
                  />
                </Suspense>
              )}
            </div>
      )}
    </ErrorBoundary>
  );
}

export default App; 